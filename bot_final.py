import discord
from discord.ext import commands
from discord import app_commands
import aiohttp
import json
import time
import uuid
import base64
import random
import asyncio
from googletrans import Translator

class Config:
    BOT_TOKEN = "MTMyMDMwMTk0MTEzMzI3OTI0Mw.GJl4Pp.HQDWOVC76-a3ryhwF-c-Gu-75LsrjCPWfE0KXw"
    SERVER_ID = 1394919544803557528
    SLOT_SERVER_ID = 1401541659874820138
    GITHUB_TOKEN = "*********************************************************************************************"
    SLOT_SERVER_INVITE = "https://discord.gg/ndDcZVPvh9"

ROLES = {"1397830372343152701": "premium", "1397830379670601820": "booster", "1397830381230620752": "member", "1401573343194517565": "premium", "1401573296507584563": "member"}
ALLOWED_ROLES = [1397830372343152701, 1397830379670601820, 1397830381230620752, 1397830366315679825, 1397660131390390414, 1401573343194517565, 1401573296507584563]

translator = Translator()
LANGUAGES = {
    "en": "English", "es": "Spanish", "fr": "French", "de": "German", "it": "Italian",
    "pt": "Portuguese", "ru": "Russian", "ja": "Japanese", "ko": "Korean", "zh": "Chinese",
    "ar": "Arabic", "hi": "Hindi", "tr": "Turkish", "pl": "Polish", "nl": "Dutch"
}

def load_data():
    try:
        with open("user_data.json", "r") as f:
            return json.load(f)
    except:
        return {}

def save_data(data):
    with open("user_data.json", "w") as f:
        json.dump(data, f, indent=2)

def get_profile(user_id):
    data = load_data()
    user_id = str(user_id)
    if user_id not in data:
        data[user_id] = {"slots": {}, "points": 0, "daily_streak": 0, "last_daily": None, "total_scripts": 0, "language": "en", "achievements": []}
    return data, data[user_id]

def load_scripts():
    try:
        with open("predefined_scripts.json", "r") as f:
            return json.load(f)
    except:
        return {"egg_randomizer": {"name": "Egg Randomizer", "url": "https://raw.githubusercontent.com/DupeNew/loader/refs/heads/main/EggRandomizer"}}

def has_role(interaction):
    return interaction.guild_id == Config.SLOT_SERVER_ID or any(role.id in ALLOWED_ROLES for role in interaction.user.roles)

def get_tier(user):
    for role in user.roles:
        if str(role.id) in ROLES:
            return ROLES[str(role.id)]
    return "member"

async def obfuscate_script(script_content):
    return f'loadstring(game:HttpGet("data:text/plain;base64,{base64.b64encode(script_content.encode()).decode()}"))()'

async def translate_text(text, target_lang, user_id):
    if target_lang == "en":
        return text
    try:
        profile = get_profile(user_id)[1]
        user_lang = profile.get("language", "en")
        if user_lang == "en":
            return text
        result = translator.translate(text, dest=user_lang)
        return result.text if result else text
    except:
        return text

class Bot(commands.Bot):
    def __init__(self):
        super().__init__(command_prefix="!", intents=discord.Intents.all())
        self.config = Config()

    async def setup_hook(self):
        for guild_id in [self.config.SERVER_ID, self.config.SLOT_SERVER_ID]:
            guild = discord.Object(id=guild_id)
            self.tree.copy_global_to(guild=guild)
            await self.tree.sync(guild=guild)
        print("Commands synced to both servers")

    async def on_ready(self):
        print(f"Bot ready: {self.user}")

    async def on_member_join(self, member):
        if member.guild.id == self.config.SLOT_SERVER_ID:
            roles = [discord.utils.get(member.guild.roles, id=role_id) for role_id in [1401573296507584563, 1401573343194517565]]
            await member.add_roles(*[role for role in roles if role])
            
            channel = discord.utils.get(member.guild.text_channels, name="general") or member.guild.system_channel
            if channel:
                embed = discord.Embed(title="Welcome to CHETOS LB Slot Server", description=f"Welcome {member.mention}! Use `/claim-slot` to create your private script channel.", color=0x00ff00)
                embed.add_field(name="Quick Start", value="1. Use `/claim-slot name:your-slot`\n2. Generate scripts with `/generate-growagarden`\n3. Need help? Use `/tutorial`", inline=False)
                try:
                    await channel.send(embed=embed)
                except:
                    pass

bot = Bot()

async def script_autocomplete(interaction, current: str):
    scripts = load_scripts()
    return [app_commands.Choice(name=f"{info.get('name', sid)} (ID: {sid})", value=sid) 
            for sid, info in scripts.items() 
            if current.lower() in sid.lower() or current.lower() in info.get("name", "").lower()][:25]

@bot.tree.command(name="claim-slot", description="Create your private script channel")
@app_commands.describe(name="Name for your slot")
async def claim_slot(interaction: discord.Interaction, name: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    if interaction.guild_id == bot.config.SERVER_ID:
        await interaction.response.send_message(f"Join the slot server to create your slot: {bot.config.SLOT_SERVER_INVITE}", ephemeral=True)
        return

    if name in profile["slots"]:
        await interaction.response.send_message(f"You already have a slot named '{name}'", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    guild = interaction.guild
    category = discord.utils.get(guild.categories, name="PRIVATE-SLOTS") or await guild.create_category("PRIVATE-SLOTS")

    overwrites = {
        guild.default_role: discord.PermissionOverwrite(read_messages=False),
        interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
        guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
    }

    for role in guild.roles:
        if role.name in ["Admin", "Moderator", "Helper"]:
            overwrites[role] = discord.PermissionOverwrite(read_messages=True)

    channel = await guild.create_text_channel(f"{interaction.user.name.lower()}-{name}", category=category, overwrites=overwrites)
    webhook = await channel.create_webhook(name=f"{interaction.user.name} Webhook")

    profile["slots"][name] = {"webhook_url": webhook.url, "tier": get_tier(interaction.user), "channel_id": channel.id, "created_time": time.time(), "generation_count": 0}
    save_data(data)

    embed = discord.Embed(title="Slot Created", description=f"Your slot '{name}' is ready in {channel.mention}", color=0x00ff00)
    embed.add_field(name="How to Generate", value="Use `/generate-growagarden` in your slot channel", inline=False)
    await interaction.followup.send(embed=embed, ephemeral=True)

    tutorial_embed = discord.Embed(title="How to Generate Scripts", color=0x0099ff)
    tutorial_embed.add_field(name="Command", value="`/generate-growagarden`", inline=False)
    tutorial_embed.add_field(name="Parameters", value="**username:** Your Roblox username\n**preset_script:** Select from dropdown", inline=False)
    tutorial_embed.add_field(name="Steps", value="1. Type the command\n2. Enter your username\n3. Select script from dropdown\n4. Execute", inline=False)
    await channel.send(f"Welcome {interaction.user.mention}!", embed=tutorial_embed)

@bot.tree.command(name="delete-slot", description="Delete your slot")
@app_commands.describe(name="Name of slot to delete")
async def delete_slot(interaction: discord.Interaction, name: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    if name not in profile["slots"]:
        await interaction.response.send_message(f"You don't have a slot named '{name}'", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    slot_info = profile["slots"].pop(name)
    save_data(data)

    channel_id = slot_info.get("channel_id")
    if channel_id:
        for guild in [interaction.guild, bot.get_guild(bot.config.SLOT_SERVER_ID)]:
            if guild:
                channel = guild.get_channel(channel_id)
                if channel:
                    try:
                        await channel.delete(reason=f"Slot deleted by {interaction.user.name}")
                        break
                    except:
                        pass

    await interaction.followup.send(f"Slot '{name}' deleted successfully", ephemeral=True)

@bot.tree.command(name="generate-growagarden", description="Generate your script")
@app_commands.describe(username="Your Roblox username", preset_script="Select script from dropdown")
@app_commands.autocomplete(preset_script=script_autocomplete)
async def generate_script(interaction: discord.Interaction, username: str, preset_script: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    slot_info = None
    for slot_name, s_info in profile["slots"].items():
        if s_info.get("channel_id") == interaction.channel.id:
            slot_info = s_info
            break

    if not slot_info:
        channel_name = interaction.channel.name
        if channel_name.startswith(interaction.user.name.lower()):
            try:
                webhooks = await interaction.channel.webhooks()
                webhook = webhooks[0] if webhooks else await interaction.channel.create_webhook(name=f"{interaction.user.name} Webhook")
                
                slot_name = channel_name.split('-', 1)[-1] if '-' in channel_name else "auto"
                profile["slots"][slot_name] = {"webhook_url": webhook.url, "tier": "member", "channel_id": interaction.channel.id, "created_time": time.time(), "generation_count": 0}
                save_data(data)
                slot_info = profile["slots"][slot_name]
                
                await interaction.response.send_message("Auto-registered your slot! Generating script...", ephemeral=True)
            except:
                await interaction.response.send_message("This is not your slot channel. Use `/claim-slot` first.", ephemeral=True)
                return
        else:
            await interaction.response.send_message("This is not your slot channel. Use `/claim-slot` first.", ephemeral=True)
            return
    else:
        await interaction.response.defer(ephemeral=True)

    scripts = load_scripts()
    if preset_script not in scripts:
        await interaction.followup.send("Invalid script selected", ephemeral=True)
        return

    script_url = scripts[preset_script].get("url", "")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(script_url) as resp:
                script_content = await resp.text() if resp.status == 200 else f'loadstring(game:HttpGet("{script_url}"))()'
    except:
        script_content = f'loadstring(game:HttpGet("{script_url}"))()'

    obfuscated = await obfuscate_script(script_content)

    profile["total_scripts"] += 1
    slot_info["generation_count"] = slot_info.get("generation_count", 0) + 1

    # Achievement tracking
    if profile["total_scripts"] == 1 and "first_script" not in profile["achievements"]:
        profile["achievements"].append("first_script")
    if profile["total_scripts"] >= 100 and "script_master" not in profile["achievements"]:
        profile["achievements"].append("script_master")

    save_data(data)

    embed1 = discord.Embed(title="1️⃣ Generated Script (Bot Data)", description="This script contains tracking data", color=0xff0000)
    embed1.add_field(name="❌ Don't Use This One", value="This is for bot data collection only", inline=False)
    embed1.add_field(name="Script Code", value=f"```lua\n{obfuscated[:800]}{'...' if len(obfuscated) > 800 else ''}\n```", inline=False)

    embed2 = discord.Embed(title="2️⃣ Original Script (Use This One)", description="✅ Use this script for actual gameplay", color=0x00ff00)
    embed2.add_field(name="✅ Use This For Playing", value="Perfect for videos, streaming, and content creation", inline=False)
    embed2.add_field(name="Script Code", value=f"```lua\n{script_content[:800]}{'...' if len(script_content) > 800 else ''}\n```", inline=False)

    info_embed = discord.Embed(title="📋 Which Script to Use?", color=0x0099ff)
    info_embed.add_field(name="Simple Rule", value="**Always use Script #2 (Original Script)**\n\n✅ For playing Roblox\n✅ For making videos\n✅ For streaming\n✅ For content creation\n\n❌ Never use Script #1", inline=False)

    await interaction.followup.send(f"{interaction.user.mention}, your scripts are ready:", embeds=[embed1, embed2, info_embed], ephemeral=True)

@bot.tree.command(name="generatepro", description="Generate script with your webhook")
@app_commands.describe(preset_script="Select script")
@app_commands.autocomplete(preset_script=script_autocomplete)
async def generate_pro(interaction: discord.Interaction, preset_script: str):
    if not has_role(interaction):
        await interaction.response.send_message("You need proper roles to use this command", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)

    scripts = load_scripts()
    if preset_script not in scripts:
        await interaction.followup.send("Invalid script selected", ephemeral=True)
        return

    script_url = scripts[preset_script].get("url", "")

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(script_url) as resp:
                script_content = await resp.text() if resp.status == 200 else f'loadstring(game:HttpGet("{script_url}"))()'
    except:
        script_content = f'loadstring(game:HttpGet("{script_url}"))()'

    obfuscated = await obfuscate_script(script_content)

    embed1 = discord.Embed(title="🔥 PRO: Generated Script (Bot Data)", description="This script contains tracking data", color=0xff0000)
    embed1.add_field(name="❌ Don't Use This One", value="This is for bot data collection only", inline=False)
    embed1.add_field(name="Script Code", value=f"```lua\n{obfuscated[:800]}{'...' if len(obfuscated) > 800 else ''}\n```", inline=False)

    embed2 = discord.Embed(title="🔥 PRO: Original Script (Use This One)", description="✅ Use this script for actual gameplay", color=0x00ff00)
    embed2.add_field(name="✅ Use This For Playing", value="Perfect for videos, streaming, and content creation", inline=False)
    embed2.add_field(name="Script Code", value=f"```lua\n{script_content[:800]}{'...' if len(script_content) > 800 else ''}\n```", inline=False)

    info_embed = discord.Embed(title="🔥 PRO: Which Script to Use?", color=0x0099ff)
    info_embed.add_field(name="Simple Rule", value="**Always use Script #2 (Original Script)**\n\n✅ For playing Roblox\n✅ For making videos\n✅ For streaming\n✅ For content creation\n\n❌ Never use Script #1", inline=False)

    await interaction.followup.send(f"{interaction.user.mention}, your PRO scripts are ready:", embeds=[embed1, embed2, info_embed], ephemeral=True)

@bot.tree.command(name="daily", description="Claim daily points")
async def daily(interaction: discord.Interaction):
    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)
    today = time.strftime("%Y-%m-%d")

    if profile["last_daily"] == today:
        await interaction.response.send_message("You already claimed your daily bonus today", ephemeral=True)
        return

    profile["points"] += 5
    profile["last_daily"] = today
    profile["daily_streak"] += 1

    # Achievement tracking
    if profile["daily_streak"] >= 7 and "daily_streak_7" not in profile["achievements"]:
        profile["achievements"].append("daily_streak_7")
    if profile["daily_streak"] >= 30 and "daily_streak_30" not in profile["achievements"]:
        profile["achievements"].append("daily_streak_30")

    save_data(data)
    await interaction.response.send_message(f"Daily bonus claimed! +5 points. Streak: {profile['daily_streak']} days", ephemeral=True)

@bot.tree.command(name="points", description="Check your points")
async def points(interaction: discord.Interaction):
    profile = get_profile(interaction.user.id)[1]
    await interaction.response.send_message(f"You have {profile['points']} points", ephemeral=True)

@bot.tree.command(name="profile", description="View your profile")
async def profile_cmd(interaction: discord.Interaction):
    profile = get_profile(interaction.user.id)[1]
    embed = discord.Embed(title=f"{interaction.user.display_name}'s Profile", color=0x0099ff)
    embed.add_field(name="Points", value=profile["points"], inline=True)
    embed.add_field(name="Scripts Generated", value=profile["total_scripts"], inline=True)
    embed.add_field(name="Daily Streak", value=profile["daily_streak"], inline=True)
    embed.add_field(name="Slots", value=len(profile["slots"]), inline=True)
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="tutorial", description="Complete setup guide")
async def tutorial(interaction: discord.Interaction):
    embed = discord.Embed(title="CHETOS LB Script Generator Guide", description="Complete setup in 3 simple steps", color=0x00ff00)
    embed.add_field(name="🏠 Step 1: Get Your Slot", value=f"**Main Server:** `/claim-slot name:your-slot-name`\n**Result:** Receive invite to slot server\n**Example:** `/claim-slot name:main-script`", inline=False)
    embed.add_field(name="🔗 Step 2: Join Slot Server", value=f"Click the invite link: {Config.SLOT_SERVER_INVITE}\nYour private channel will be created automatically", inline=False)
    embed.add_field(name="⚡ Step 3: Generate Scripts", value="**In your slot channel:**\n`/generate-growagarden username:YourRobloxName preset_script:script_id`\n\n**Both parameters required:**\n• username: Your Roblox username\n• preset_script: Select from dropdown menu", inline=False)
    embed.add_field(name="🔥 PRO Mode (No Slot Needed)", value="**Skip slots entirely:**\n`/generatepro preset_script:script_id`\n\n**Use anywhere in any server**", inline=False)
    embed.add_field(name="📋 Available Scripts", value="Scripts appear in dropdown when you type commands. Click `preset_script` field to see all options.", inline=False)
    embed.add_field(name="🆘 Need Help?", value="Create a support ticket or ask in general chat", inline=False)
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="addscript", description="Add new script (Admin only)")
@app_commands.describe(script_id="Unique ID for script", name="Display name", url="Script URL")
async def add_script(interaction: discord.Interaction, script_id: str, name: str, url: str):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Admin only command", ephemeral=True)
        return

    scripts = load_scripts()
    scripts[script_id] = {"name": name, "url": url}

    try:
        with open("predefined_scripts.json", "w") as f:
            json.dump(scripts, f, indent=2)
        await interaction.response.send_message(f"Script '{name}' added with ID '{script_id}'", ephemeral=True)
    except:
        await interaction.response.send_message("Failed to save script", ephemeral=True)

@bot.tree.command(name="sync", description="Sync commands (Admin only)")
async def sync_commands(interaction: discord.Interaction):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Admin only command", ephemeral=True)
        return

    await interaction.response.defer(ephemeral=True)
    try:
        for guild_id in [bot.config.SERVER_ID, bot.config.SLOT_SERVER_ID]:
            guild = discord.Object(id=guild_id)
            bot.tree.copy_global_to(guild=guild)
            await bot.tree.sync(guild=guild)
        await interaction.followup.send("Commands synced to both servers", ephemeral=True)
    except Exception as e:
        await interaction.followup.send(f"Sync failed: {e}", ephemeral=True)

@bot.tree.command(name="language", description="Set your language preference")
@app_commands.describe(language="Choose your language")
@app_commands.choices(language=[
    app_commands.Choice(name="English", value="en"),
    app_commands.Choice(name="Spanish", value="es"),
    app_commands.Choice(name="French", value="fr"),
    app_commands.Choice(name="German", value="de"),
    app_commands.Choice(name="Italian", value="it"),
    app_commands.Choice(name="Portuguese", value="pt"),
    app_commands.Choice(name="Russian", value="ru"),
    app_commands.Choice(name="Japanese", value="ja"),
    app_commands.Choice(name="Korean", value="ko"),
    app_commands.Choice(name="Chinese", value="zh")
])
async def set_language(interaction: discord.Interaction, language: str):
    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)
    profile["language"] = language
    save_data(data)

    lang_name = LANGUAGES.get(language, language)
    response = f"Language set to {lang_name}"
    translated = await translate_text(response, language, user_id)
    await interaction.response.send_message(translated, ephemeral=True)

@bot.tree.command(name="leaderboard", description="View top users")
@app_commands.describe(category="What to rank by")
@app_commands.choices(category=[
    app_commands.Choice(name="Points", value="points"),
    app_commands.Choice(name="Scripts Generated", value="scripts"),
    app_commands.Choice(name="Daily Streak", value="streak")
])
async def leaderboard(interaction: discord.Interaction, category: str = "points"):
    data = load_data()

    if category == "points":
        sorted_users = sorted(data.items(), key=lambda x: x[1].get("points", 0), reverse=True)[:10]
        title = "Top 10 Points Leaders"
        field_name = "Points"
    elif category == "scripts":
        sorted_users = sorted(data.items(), key=lambda x: x[1].get("total_scripts", 0), reverse=True)[:10]
        title = "Top 10 Script Generators"
        field_name = "Scripts"
    else:
        sorted_users = sorted(data.items(), key=lambda x: x[1].get("daily_streak", 0), reverse=True)[:10]
        title = "Top 10 Daily Streaks"
        field_name = "Days"

    embed = discord.Embed(title=title, color=0xffd700)

    if not sorted_users:
        embed.description = "No data available yet"
    else:
        leaderboard_text = ""
        for i, (user_id, profile) in enumerate(sorted_users, 1):
            try:
                user = bot.get_user(int(user_id))
                username = user.display_name if user else f"User {user_id[:8]}"
                value = profile.get(category, 0)
                medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}."
                leaderboard_text += f"{medal} {username}: {value}\n"
            except:
                continue

        embed.add_field(name=field_name, value=leaderboard_text or "No data", inline=False)

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="giveaway", description="Start a giveaway (Admin only)")
@app_commands.describe(prize="What to give away", duration="Duration in minutes", winners="Number of winners")
async def giveaway(interaction: discord.Interaction, prize: str, duration: int, winners: int = 1):
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("Admin only command", ephemeral=True)
        return

    embed = discord.Embed(title="🎉 GIVEAWAY 🎉", description=f"**Prize:** {prize}\n**Winners:** {winners}\n**Duration:** {duration} minutes", color=0xff6b6b)
    embed.add_field(name="How to Enter", value="React with 🎉 to enter!", inline=False)
    embed.set_footer(text=f"Ends in {duration} minutes")

    await interaction.response.send_message(embed=embed)
    message = await interaction.original_response()
    await message.add_reaction("🎉")

@bot.tree.command(name="ticket", description="Create a support ticket")
@app_commands.describe(reason="Reason for the ticket")
async def create_ticket(interaction: discord.Interaction, reason: str):
    guild = interaction.guild
    category = discord.utils.get(guild.categories, name="TICKETS") or await guild.create_category("TICKETS")

    overwrites = {
        guild.default_role: discord.PermissionOverwrite(read_messages=False),
        interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
        guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
    }

    for role in guild.roles:
        if role.name in ["Admin", "Moderator", "Helper"]:
            overwrites[role] = discord.PermissionOverwrite(read_messages=True)

    channel = await guild.create_text_channel(f"ticket-{interaction.user.name}", category=category, overwrites=overwrites)

    embed = discord.Embed(title="Support Ticket Created", description=f"**User:** {interaction.user.mention}\n**Reason:** {reason}", color=0x00ff00)
    embed.add_field(name="Next Steps", value="Staff will assist you shortly. Please describe your issue in detail.", inline=False)

    await channel.send(f"{interaction.user.mention}", embed=embed)
    await interaction.response.send_message(f"Ticket created: {channel.mention}", ephemeral=True)

@bot.tree.command(name="close-ticket", description="Close a ticket (Staff only)")
async def close_ticket(interaction: discord.Interaction):
    if not any(role.name in ["Admin", "Moderator", "Helper"] for role in interaction.user.roles):
        await interaction.response.send_message("Staff only command", ephemeral=True)
        return

    if not interaction.channel.name.startswith("ticket-"):
        await interaction.response.send_message("This is not a ticket channel", ephemeral=True)
        return

    await interaction.response.send_message("Ticket will be closed in 5 seconds...")
    await asyncio.sleep(5)
    await interaction.channel.delete(reason=f"Ticket closed by {interaction.user.name}")

@bot.tree.command(name="shop", description="View points shop")
async def shop(interaction: discord.Interaction):
    embed = discord.Embed(title="🛒 Points Shop", color=0x00ff00)
    embed.add_field(name="Premium Role (100 points)", value="Get premium benefits and no cooldowns", inline=False)
    embed.add_field(name="Custom Slot Name (50 points)", value="Choose a custom name for your slot", inline=False)
    embed.add_field(name="Priority Support (25 points)", value="Get faster support responses", inline=False)
    embed.set_footer(text="Use /buy <item> to purchase")
    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="buy", description="Buy item from shop")
@app_commands.describe(item="Item to buy")
@app_commands.choices(item=[
    app_commands.Choice(name="Premium Role (100 points)", value="premium"),
    app_commands.Choice(name="Custom Slot Name (50 points)", value="custom_name"),
    app_commands.Choice(name="Priority Support (25 points)", value="priority")
])
async def buy_item(interaction: discord.Interaction, item: str):
    user_id = str(interaction.user.id)
    data, profile = get_profile(user_id)

    prices = {"premium": 100, "custom_name": 50, "priority": 25}
    price = prices.get(item, 0)

    if profile["points"] < price:
        await interaction.response.send_message(f"Not enough points. You need {price} points but have {profile['points']}", ephemeral=True)
        return

    profile["points"] -= price

    if item == "premium":
        premium_role = discord.utils.get(interaction.guild.roles, name="Premium")
        if premium_role:
            await interaction.user.add_roles(premium_role)
        profile["achievements"].append("premium_purchased")

    save_data(data)
    await interaction.response.send_message(f"Successfully purchased {item}! Remaining points: {profile['points']}", ephemeral=True)

@bot.tree.command(name="achievements", description="View your achievements")
async def achievements(interaction: discord.Interaction):
    profile = get_profile(interaction.user.id)[1]
    achievements = profile.get("achievements", [])

    embed = discord.Embed(title=f"{interaction.user.display_name}'s Achievements", color=0xffd700)

    all_achievements = {
        "first_script": "🎯 First Script Generated",
        "daily_streak_7": "🔥 7 Day Streak",
        "daily_streak_30": "💎 30 Day Streak",
        "premium_purchased": "👑 Premium Member",
        "script_master": "⚡ Script Master (100+ scripts)"
    }

    if not achievements:
        embed.description = "No achievements yet. Keep using the bot to unlock them!"
    else:
        achievement_text = ""
        for ach in achievements:
            if ach in all_achievements:
                achievement_text += f"{all_achievements[ach]}\n"
        embed.add_field(name="Unlocked", value=achievement_text or "None", inline=False)

    await interaction.response.send_message(embed=embed, ephemeral=True)

if __name__ == "__main__":
    bot.run(Config.BOT_TOKEN)
