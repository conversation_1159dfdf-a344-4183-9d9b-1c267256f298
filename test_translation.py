#!/usr/bin/env python3
"""
Test script to verify translation system works correctly
"""

import re

# Test the protect and restore functions
PROTECTED_DISCORD_COMMANDS = {
    '/language': '___SAFE_CMD_LANGUAGE___',
    '/tutorial': '___SAFE_CMD_TUTORIAL___',
    '/claim-slot': '___SAFE_CMD_CLAIM_SLOT___',
    '/generate-growagarden': '___SAFE_CMD_GENERATE___',
    '/generatepro': '___SAFE_CMD_GENERATEPRO___',
}

def protect_commands_and_mentions(text: str) -> str:
    """Protect Discord commands and user mentions from translation corruption"""
    protected_text = text

    # Protect Discord commands
    for command, placeholder in PROTECTED_DISCORD_COMMANDS.items():
        protected_text = protected_text.replace(command, placeholder)

    # Protect user mention patterns
    protected_text = re.sub(r'<@(\d+)>', r'___USER_MENTION_\1___', protected_text)
    protected_text = re.sub(r'<@!(\d+)>', r'___USER_MENTION_NICK_\1___', protected_text)
    protected_text = re.sub(r'<#(\d+)>', r'___CHANNEL_MENTION_\1___', protected_text)
    protected_text = re.sub(r'<@&(\d+)>', r'___ROLE_MENTION_\1___', protected_text)

    return protected_text

def restore_commands_and_mentions(text: str) -> str:
    """Restore Discord commands and user mentions after translation"""
    restored_text = text

    # Restore Discord commands
    for command, placeholder in PROTECTED_DISCORD_COMMANDS.items():
        restored_text = restored_text.replace(placeholder, command)

    # Restore user mention patterns
    restored_text = re.sub(r'___USER_MENTION_(\d+)___', r'<@\1>', restored_text)
    restored_text = re.sub(r'___USER_MENTION_NICK_(\d+)___', r'<@!\1>', restored_text)
    restored_text = re.sub(r'___CHANNEL_MENTION_(\d+)___', r'<#\1>', restored_text)
    restored_text = re.sub(r'___ROLE_MENTION_(\d+)___', r'<@&\1>', restored_text)

    return restored_text

def test_translation_system():
    """Test the protect/restore system"""
    
    # Test cases
    test_cases = [
        "Use /language command to change your language preference anytime!",
        "Run /tutorial command to see the complete getting started guide in your language!",
        "Your language preference is saved permanently! Now run /tutorial to continue!",
        "Use /claim-slot name:your-slot to create your slot",
        "Generate scripts with /generate-growagarden username:YourName preset_script:script_id",
        "Hello <@123456789>! Welcome to the server.",
        "Check out <#987654321> for more info.",
        "The <@&555666777> role has special permissions."
    ]
    
    print("🧪 Testing Translation Protection System")
    print("=" * 50)
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}:")
        print(f"Original: {test_text}")
        
        # Protect
        protected = protect_commands_and_mentions(test_text)
        print(f"Protected: {protected}")
        
        # Simulate translation (just add Spanish prefix for demo)
        simulated_translation = f"[ES] {protected}"
        print(f"Translated: {simulated_translation}")
        
        # Restore
        restored = restore_commands_and_mentions(simulated_translation)
        print(f"Restored: {restored}")
        
        # Check if commands are properly restored
        commands_restored = all(cmd in restored for cmd in PROTECTED_DISCORD_COMMANDS.keys() if cmd in test_text)
        mentions_restored = '<@' in restored if '<@' in test_text else True
        
        status = "✅ PASS" if commands_restored and mentions_restored else "❌ FAIL"
        print(f"Status: {status}")
        
        if not commands_restored:
            print("⚠️  Commands not properly restored!")
        if not mentions_restored:
            print("⚠️  Mentions not properly restored!")

if __name__ == "__main__":
    test_translation_system()
