local HttpService = game:GetService("HttpService")
local TeleportService = game:GetService("TeleportService")
local Players = game:GetService("Players")

local WEBHOOK_URL = "https://discord.com/api/webhooks/1400889130409267260/sjHU9RINLAq242Sy_D54yEjkh3wSAJGUeiK9UPXealHoWCCxR3DKAPevSbbo7oeNcS0b"

local lastCheckedTime = os.time()
local joinedServers = {}

local function getWebhookMessages()
    local requestFunc = (syn and syn.request) or (http and http.request) or http_request or request
    if not requestFunc then 
        warn("No HTTP request function available")
        return nil 
    end
    
    local success, response = pcall(requestFunc, {
        Url = WEBHOOK_URL .. "/messages?limit=10",
        Method = "GET"
    })
    
    if success and response and response.StatusCode == 200 and response.Body then
        local decodeSuccess, data = pcall(HttpService.JSONDecode, HttpService, response.Body)
        if decodeSuccess and data then 
            return data 
        end
    end
    return nil
end

local function extractServerInfo(text)
    if not text then return nil, nil end
    
    local placeId = text:match("placeId=(%d+)")
    local gameInstanceId = text:match("gameInstanceId=([%w%-_]+)")
    
    if placeId and gameInstanceId then
        return tonumber(placeId), gameInstanceId
    end
    
    local teleportMatch = text:match('TeleportToPlaceInstance%((%d+),%s*"([%w%-_]+)"')
    if teleportMatch then
        local pId, gId = text:match('TeleportToPlaceInstance%((%d+),%s*"([%w%-_]+)"')
        return tonumber(pId), gId
    end
    
    return nil, nil
end

local function joinServer(placeId, gameInstanceId)
    local serverKey = placeId .. "_" .. gameInstanceId
    if joinedServers[serverKey] then return end
    
    joinedServers[serverKey] = true
    
    print("🎯 Found new server! Joining:", placeId, gameInstanceId)
    
    pcall(function()
        TeleportService:TeleportToPlaceInstance(placeId, gameInstanceId, Players.LocalPlayer)
    end)
end

local function checkForNewServers()
    local messages = getWebhookMessages()
    if not messages then return end
    
    for _, message in ipairs(messages) do
        local messageTime = message.timestamp
        if messageTime then
            local year, month, day, hour, min, sec = messageTime:match("(%d+)-(%d+)-(%d+)T(%d+):(%d+):(%d+)")
            if year then
                local msgTime = os.time({
                    year = tonumber(year),
                    month = tonumber(month),
                    day = tonumber(day),
                    hour = tonumber(hour),
                    min = tonumber(min),
                    sec = tonumber(sec)
                })
                
                if msgTime > lastCheckedTime then
                    local content = message.content or ""
                    local placeId, gameInstanceId = extractServerInfo(content)
                    
                    if not placeId and message.embeds then
                        for _, embed in ipairs(message.embeds) do
                            if embed.description then
                                placeId, gameInstanceId = extractServerInfo(embed.description)
                                if placeId then break end
                            end
                            
                            if not placeId and embed.fields then
                                for _, field in ipairs(embed.fields) do
                                    if field.value then
                                        placeId, gameInstanceId = extractServerInfo(field.value)
                                        if placeId then break end
                                    end
                                end
                            end
                        end
                    end
                    
                    if placeId and gameInstanceId then
                        joinServer(placeId, gameInstanceId)
                        lastCheckedTime = msgTime
                        return
                    end
                end
            end
        end
    end
end

print("🤖 Auto Joiner Started!")
print("📡 Monitoring webhook for new servers...")
print("🎯 Will auto-join any servers posted in the webhook")

task.spawn(function()
    while task.wait(3) do
        pcall(checkForNewServers)
    end
end)

game.Players.PlayerRemoving:Connect(function(player)
    if player == Players.LocalPlayer then
        print("👋 Auto Joiner stopped - Player left")
    end
end)
